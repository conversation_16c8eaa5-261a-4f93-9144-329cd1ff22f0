/**
 * 增强回归分析测试
 * 版本: v2.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v2.0.0: 初始版本，测试增强回归分析功能
 */

// 测试数据 - 模拟身高和体重的线性关系
const testDataX = [160, 165, 170, 175, 180, 185, 190]; // 身高 (cm)
const testDataY = [50, 55, 65, 70, 80, 85, 95]; // 体重 (kg)

// 模拟统计计算器的增强回归分析功能
function testEnhancedRegression() {
    console.log('开始测试增强回归分析功能...');
    
    try {
        // 模拟增强回归分析计算
        const n = testDataX.length;
        const meanX = testDataX.reduce((a, b) => a + b, 0) / n;
        const meanY = testDataY.reduce((a, b) => a + b, 0) / n;
        
        // 计算回归系数
        let numerator = 0;
        let denominator = 0;
        for (let i = 0; i < n; i++) {
            numerator += (testDataX[i] - meanX) * (testDataY[i] - meanY);
            denominator += (testDataX[i] - meanX) * (testDataX[i] - meanX);
        }
        
        const slope = numerator / denominator;
        const intercept = meanY - slope * meanX;
        
        // 计算拟合值和残差
        const fittedValues = testDataX.map(x => slope * x + intercept);
        const residuals = testDataY.map((y, i) => y - fittedValues[i]);
        
        // 计算R²
        const SST = testDataY.reduce((sum, y) => sum + (y - meanY) ** 2, 0);
        const SSE = residuals.reduce((sum, r) => sum + r ** 2, 0);
        const SSR = SST - SSE;
        const rSquared = SSR / SST;
        const adjustedRSquared = 1 - (SSE / (n - 2)) / (SST / (n - 1));
        
        // 计算标准误
        const MSE = SSE / (n - 2);
        const standardError = Math.sqrt(MSE);
        
        // 计算回归系数的标准误
        const sxx = testDataX.reduce((sum, x) => sum + (x - meanX) ** 2, 0);
        const slopeStandardError = standardError / Math.sqrt(sxx);
        const interceptStandardError = standardError * Math.sqrt(1/n + (meanX * meanX) / sxx);
        
        // 计算t统计量
        const slopeT = slope / slopeStandardError;
        const interceptT = intercept / interceptStandardError;
        
        // 计算F统计量
        const MSR = SSR / 1;
        const fStatistic = MSR / MSE;
        
        console.log('✓ 基本回归计算测试通过');
        console.log(`  斜率: ${slope.toFixed(4)}`);
        console.log(`  截距: ${intercept.toFixed(4)}`);
        console.log(`  R²: ${rSquared.toFixed(4)}`);
        console.log(`  调整R²: ${adjustedRSquared.toFixed(4)}`);
        console.log(`  F统计量: ${fStatistic.toFixed(4)}`);
        
        // 测试残差分析
        const residualMean = residuals.reduce((a, b) => a + b, 0) / residuals.length;
        const residualStd = Math.sqrt(residuals.reduce((sum, r) => sum + (r - residualMean) ** 2, 0) / (residuals.length - 1));
        const standardizedResiduals = residuals.map(r => r / standardError);
        
        console.log('✓ 残差分析测试通过');
        console.log(`  残差均值: ${residualMean.toFixed(6)}`);
        console.log(`  残差标准差: ${residualStd.toFixed(4)}`);
        console.log(`  标准化残差范围: [${Math.min(...standardizedResiduals).toFixed(3)}, ${Math.max(...standardizedResiduals).toFixed(3)}]`);
        
        // 测试异常值检测
        const outliers = standardizedResiduals.filter(sr => Math.abs(sr) > 2);
        console.log('✓ 异常值检测测试通过');
        console.log(`  检测到 ${outliers.length} 个异常值`);
        
        // 测试置信区间计算
        const tCritical = 2.571; // 简化的t临界值 (df=5, α=0.05)
        const slopeCI = [
            slope - tCritical * slopeStandardError,
            slope + tCritical * slopeStandardError
        ];
        const interceptCI = [
            intercept - tCritical * interceptStandardError,
            intercept + tCritical * interceptStandardError
        ];
        
        console.log('✓ 置信区间计算测试通过');
        console.log(`  斜率95%置信区间: [${slopeCI[0].toFixed(4)}, ${slopeCI[1].toFixed(4)}]`);
        console.log(`  截距95%置信区间: [${interceptCI[0].toFixed(4)}, ${interceptCI[1].toFixed(4)}]`);
        
        return true;
        
    } catch (error) {
        console.error('✗ 增强回归分析测试失败:', error.message);
        return false;
    }
}

// 测试残差正态性检验
function testResidualNormality() {
    console.log('\n开始测试残差正态性检验...');
    
    try {
        // 模拟正态分布的残差
        const normalResiduals = [0.1, -0.2, 0.3, -0.1, 0.2, -0.3, 0.1];
        
        // 计算偏度和峰度
        const mean = normalResiduals.reduce((a, b) => a + b, 0) / normalResiduals.length;
        const variance = normalResiduals.reduce((sum, r) => sum + (r - mean) ** 2, 0) / (normalResiduals.length - 1);
        const std = Math.sqrt(variance);
        
        // 简化的偏度计算
        const skewness = normalResiduals.reduce((sum, r) => sum + Math.pow((r - mean) / std, 3), 0) / normalResiduals.length;
        
        // 简化的峰度计算
        const kurtosis = normalResiduals.reduce((sum, r) => sum + Math.pow((r - mean) / std, 4), 0) / normalResiduals.length;
        
        console.log('✓ 正态性统计量计算测试通过');
        console.log(`  偏度: ${skewness.toFixed(4)}`);
        console.log(`  峰度: ${kurtosis.toFixed(4)}`);
        
        // 判断正态性
        const isNormal = Math.abs(skewness) < 1 && Math.abs(kurtosis - 3) < 2;
        console.log(`  正态性判断: ${isNormal ? '通过' : '不通过'}`);
        
        return true;
        
    } catch (error) {
        console.error('✗ 残差正态性检验测试失败:', error.message);
        return false;
    }
}

// 测试图表数据准备
function testChartDataPreparation() {
    console.log('\n开始测试图表数据准备...');
    
    try {
        // 模拟残差vs拟合值数据
        const fittedValues = [52.5, 57.5, 67.5, 72.5, 82.5, 87.5, 97.5];
        const residuals = [-2.5, -2.5, -2.5, -2.5, -2.5, -2.5, -2.5];
        
        // 准备散点图数据
        const scatterData = [];
        for (let i = 0; i < fittedValues.length; i++) {
            scatterData.push({ x: fittedValues[i], y: residuals[i] });
        }
        
        console.log('✓ 散点图数据准备测试通过');
        console.log(`  数据点数量: ${scatterData.length}`);
        
        // 准备Q-Q图数据
        const sortedResiduals = [...residuals].sort((a, b) => a - b);
        const qqData = [];
        for (let i = 0; i < sortedResiduals.length; i++) {
            const p = (i + 0.5) / sortedResiduals.length;
            // 简化的正态分位数
            const theoreticalQuantile = p < 0.5 ? -1 : 1;
            qqData.push({ x: theoreticalQuantile, y: sortedResiduals[i] });
        }
        
        console.log('✓ Q-Q图数据准备测试通过');
        console.log(`  Q-Q数据点数量: ${qqData.length}`);
        
        return true;
        
    } catch (error) {
        console.error('✗ 图表数据准备测试失败:', error.message);
        return false;
    }
}

// 运行所有增强回归分析测试
function runEnhancedRegressionTests() {
    console.log('=== 增强回归分析功能测试 ===\n');
    
    let allPassed = true;
    
    // 运行各项测试
    allPassed &= testEnhancedRegression();
    allPassed &= testResidualNormality();
    allPassed &= testChartDataPreparation();
    
    console.log('\n=== 测试总结 ===');
    if (allPassed) {
        console.log('🎉 所有增强回归分析测试通过！');
    } else {
        console.log('❌ 部分测试失败');
    }
    
    return allPassed;
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.runEnhancedRegressionTests = runEnhancedRegressionTests;
    window.testEnhancedRegression = testEnhancedRegression;
    window.testResidualNormality = testResidualNormality;
    window.testChartDataPreparation = testChartDataPreparation;
}
