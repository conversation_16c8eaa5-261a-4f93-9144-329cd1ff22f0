/**
 * 统计计算器测试
 * 版本: v1.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v1.0.0: 初始版本，测试统计计算器的核心功能
 */

// 测试数据
const testData = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
const testDataX = [1, 2, 3, 4, 5];
const testDataY = [2, 4, 6, 8, 10];

// 模拟simple-statistics库的基本功能
const mockSS = {
    mean: (data) => data.reduce((a, b) => a + b, 0) / data.length,
    median: (data) => {
        const sorted = [...data].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);
        return sorted.length % 2 !== 0 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
    },
    mode: (data) => {
        const frequency = {};
        let maxFreq = 0;
        let mode = data[0];
        
        data.forEach(value => {
            frequency[value] = (frequency[value] || 0) + 1;
            if (frequency[value] > maxFreq) {
                maxFreq = frequency[value];
                mode = value;
            }
        });
        
        return mode;
    },
    standardDeviation: (data) => {
        const mean = mockSS.mean(data);
        const variance = data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / (data.length - 1);
        return Math.sqrt(variance);
    },
    variance: (data) => {
        const mean = mockSS.mean(data);
        return data.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / (data.length - 1);
    },
    min: (data) => Math.min(...data),
    max: (data) => Math.max(...data),
    sum: (data) => data.reduce((a, b) => a + b, 0),
    quantile: (data, p) => {
        const sorted = [...data].sort((a, b) => a - b);
        const index = p * (sorted.length - 1);
        const lower = Math.floor(index);
        const upper = Math.ceil(index);
        const weight = index % 1;
        
        if (upper >= sorted.length) return sorted[sorted.length - 1];
        return sorted[lower] * (1 - weight) + sorted[upper] * weight;
    },
    sampleSkewness: (data) => {
        const mean = mockSS.mean(data);
        const std = mockSS.standardDeviation(data);
        const n = data.length;
        const skewness = data.reduce((sum, value) => sum + Math.pow((value - mean) / std, 3), 0) / n;
        return skewness * Math.sqrt(n * (n - 1)) / (n - 2);
    },
    sampleKurtosis: (data) => {
        const mean = mockSS.mean(data);
        const std = mockSS.standardDeviation(data);
        const n = data.length;
        const kurtosis = data.reduce((sum, value) => sum + Math.pow((value - mean) / std, 4), 0) / n;
        return ((n - 1) / ((n - 2) * (n - 3))) * ((n + 1) * kurtosis - 3 * (n - 1)) + 3;
    },
    sampleCorrelation: (x, y) => {
        const meanX = mockSS.mean(x);
        const meanY = mockSS.mean(y);
        const numerator = x.reduce((sum, xi, i) => sum + (xi - meanX) * (y[i] - meanY), 0);
        const denomX = Math.sqrt(x.reduce((sum, xi) => sum + Math.pow(xi - meanX, 2), 0));
        const denomY = Math.sqrt(y.reduce((sum, yi) => sum + Math.pow(yi - meanY, 2), 0));
        return numerator / (denomX * denomY);
    },
    linearRegression: (pairs) => {
        const n = pairs.length;
        const sumX = pairs.reduce((sum, pair) => sum + pair[0], 0);
        const sumY = pairs.reduce((sum, pair) => sum + pair[1], 0);
        const sumXY = pairs.reduce((sum, pair) => sum + pair[0] * pair[1], 0);
        const sumXX = pairs.reduce((sum, pair) => sum + pair[0] * pair[0], 0);
        
        const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
        const intercept = (sumY - slope * sumX) / n;
        
        return { m: slope, b: intercept };
    },
    rSquared: (pairs, regression) => {
        const yMean = pairs.reduce((sum, pair) => sum + pair[1], 0) / pairs.length;
        const totalSumSquares = pairs.reduce((sum, pair) => sum + Math.pow(pair[1] - yMean, 2), 0);
        const residualSumSquares = pairs.reduce((sum, pair) => {
            const predicted = regression.m * pair[0] + regression.b;
            return sum + Math.pow(pair[1] - predicted, 2);
        }, 0);
        
        return 1 - (residualSumSquares / totalSumSquares);
    }
};

// 模拟统计计算器
class MockStatisticsCalculator {
    constructor() {
        this.ss = mockSS;
    }

    calculateDescriptiveStats(data) {
        if (!data || data.length === 0) {
            throw new Error('数据为空');
        }

        const validData = data.filter(value => !isNaN(value) && value !== null && value !== undefined);
        
        if (validData.length === 0) {
            throw new Error('没有有效的数值数据');
        }

        return {
            count: validData.length,
            mean: this.ss.mean(validData),
            median: this.ss.median(validData),
            mode: this.ss.mode(validData),
            standardDeviation: this.ss.standardDeviation(validData),
            variance: this.ss.variance(validData),
            min: this.ss.min(validData),
            max: this.ss.max(validData),
            range: this.ss.max(validData) - this.ss.min(validData),
            quartiles: {
                q1: this.ss.quantile(validData, 0.25),
                q2: this.ss.quantile(validData, 0.5),
                q3: this.ss.quantile(validData, 0.75)
            },
            skewness: this.ss.sampleSkewness(validData),
            kurtosis: this.ss.sampleKurtosis(validData),
            sum: this.ss.sum(validData)
        };
    }

    calculateCorrelation(x, y) {
        if (!x || !y || x.length === 0 || y.length === 0) {
            throw new Error('数据为空');
        }

        if (x.length !== y.length) {
            throw new Error('两个变量的数据长度不一致');
        }

        const validPairs = [];
        for (let i = 0; i < x.length; i++) {
            if (!isNaN(x[i]) && !isNaN(y[i]) && x[i] !== null && y[i] !== null) {
                validPairs.push([x[i], y[i]]);
            }
        }

        if (validPairs.length < 2) {
            throw new Error('有效数据点不足，无法计算相关系数');
        }

        const validX = validPairs.map(pair => pair[0]);
        const validY = validPairs.map(pair => pair[1]);
        const correlation = this.ss.sampleCorrelation(validX, validY);
        
        return {
            correlation: correlation,
            n: validPairs.length,
            interpretation: this.interpretCorrelation(correlation),
            strength: this.getCorrelationStrength(correlation)
        };
    }

    interpretCorrelation(correlation) {
        const abs = Math.abs(correlation);
        let strength = '';
        
        if (abs >= 0.9) strength = '非常强';
        else if (abs >= 0.7) strength = '强';
        else if (abs >= 0.5) strength = '中等';
        else if (abs >= 0.3) strength = '弱';
        else strength = '非常弱';
        
        const direction = correlation > 0 ? '正' : '负';
        return `${direction}相关，强度为${strength}（r = ${correlation.toFixed(3)}）`;
    }

    getCorrelationStrength(correlation) {
        const abs = Math.abs(correlation);
        
        if (abs >= 0.9) return 'very_strong';
        else if (abs >= 0.7) return 'strong';
        else if (abs >= 0.5) return 'moderate';
        else if (abs >= 0.3) return 'weak';
        else return 'very_weak';
    }
}

// 测试函数
function runStatisticsTests() {
    console.log('开始运行统计计算器测试...');
    
    const calculator = new MockStatisticsCalculator();
    let testsPassed = 0;
    let totalTests = 0;

    // 测试描述统计
    try {
        totalTests++;
        const stats = calculator.calculateDescriptiveStats(testData);
        
        console.assert(stats.count === 10, '样本量测试失败');
        console.assert(Math.abs(stats.mean - 5.5) < 0.001, '均值测试失败');
        console.assert(Math.abs(stats.median - 5.5) < 0.001, '中位数测试失败');
        console.assert(stats.min === 1, '最小值测试失败');
        console.assert(stats.max === 10, '最大值测试失败');
        console.assert(stats.range === 9, '范围测试失败');
        
        console.log('✓ 描述统计测试通过');
        testsPassed++;
    } catch (error) {
        console.error('✗ 描述统计测试失败:', error.message);
    }

    // 测试相关分析
    try {
        totalTests++;
        const correlation = calculator.calculateCorrelation(testDataX, testDataY);
        
        console.assert(Math.abs(correlation.correlation - 1) < 0.001, '完全正相关测试失败');
        console.assert(correlation.n === 5, '样本量测试失败');
        console.assert(correlation.strength === 'very_strong', '相关强度测试失败');
        
        console.log('✓ 相关分析测试通过');
        testsPassed++;
    } catch (error) {
        console.error('✗ 相关分析测试失败:', error.message);
    }

    // 测试错误处理
    try {
        totalTests++;
        
        // 测试空数据
        try {
            calculator.calculateDescriptiveStats([]);
            console.error('✗ 空数据错误处理测试失败');
        } catch (error) {
            console.log('✓ 空数据错误处理测试通过');
        }
        
        // 测试长度不一致的数据
        try {
            calculator.calculateCorrelation([1, 2, 3], [1, 2]);
            console.error('✗ 长度不一致错误处理测试失败');
        } catch (error) {
            console.log('✓ 长度不一致错误处理测试通过');
        }
        
        testsPassed++;
    } catch (error) {
        console.error('✗ 错误处理测试失败:', error.message);
    }

    console.log(`\n测试完成: ${testsPassed}/${totalTests} 通过`);
    return testsPassed === totalTests;
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.runStatisticsTests = runStatisticsTests;
}
