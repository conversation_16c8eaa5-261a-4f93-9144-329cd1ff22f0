/**
 * 变量选择功能测试
 * 版本: v2.1.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v2.1.0: 初始版本，测试变量选择和用户交互功能
 */

// 模拟变量选择功能
function testVariableSelection() {
    console.log('开始测试变量选择功能...');
    
    try {
        // 模拟数据列
        const mockColumns = ['身高', '体重', '年龄', '收入', '评分'];
        
        // 模拟变量选择状态
        const selectedVariables = {
            general: [],
            xVariables: [],
            yVariables: []
        };
        
        // 测试一般变量选择
        selectedVariables.general = ['身高', '体重', '年龄'];
        console.log('✓ 一般变量选择测试通过');
        console.log(`  选中变量: ${selectedVariables.general.join(', ')}`);
        
        // 测试回归变量选择
        selectedVariables.xVariables = ['身高', '年龄'];
        selectedVariables.yVariables = ['体重'];
        console.log('✓ 回归变量选择测试通过');
        console.log(`  自变量: ${selectedVariables.xVariables.join(', ')}`);
        console.log(`  因变量: ${selectedVariables.yVariables.join(', ')}`);
        
        return true;
        
    } catch (error) {
        console.error('✗ 变量选择测试失败:', error.message);
        return false;
    }
}

// 测试变量选择验证
function testVariableValidation() {
    console.log('\n开始测试变量选择验证...');
    
    try {
        // 模拟验证函数
        function validateAnalysisSelection(selectedAnalyses, selectedVariables) {
            // 检查是否选择了变量
            if (selectedVariables.general.length === 0) {
                return { valid: false, message: '请至少选择一个变量进行分析' };
            }

            // 如果选择了回归分析，验证回归变量
            if (selectedAnalyses.includes('regression')) {
                if (selectedVariables.xVariables.length === 0 || selectedVariables.yVariables.length === 0) {
                    return { valid: false, message: '回归分析需要至少选择一个自变量和一个因变量' };
                }
            }

            // 检查相关分析是否有足够的变量
            if (selectedAnalyses.includes('correlation') && selectedVariables.general.length < 2) {
                return { valid: false, message: '相关分析需要至少选择两个变量' };
            }

            return { valid: true, message: '验证通过' };
        }
        
        // 测试用例1: 无变量选择
        let result = validateAnalysisSelection(['basic-stats'], { general: [], xVariables: [], yVariables: [] });
        console.assert(!result.valid, '无变量选择验证失败');
        console.log('✓ 无变量选择验证测试通过');
        
        // 测试用例2: 回归分析缺少变量
        result = validateAnalysisSelection(['regression'], { general: ['身高'], xVariables: [], yVariables: [] });
        console.assert(!result.valid, '回归变量缺失验证失败');
        console.log('✓ 回归变量缺失验证测试通过');
        
        // 测试用例3: 相关分析变量不足
        result = validateAnalysisSelection(['correlation'], { general: ['身高'], xVariables: [], yVariables: [] });
        console.assert(!result.valid, '相关分析变量不足验证失败');
        console.log('✓ 相关分析变量不足验证测试通过');
        
        // 测试用例4: 正常情况
        result = validateAnalysisSelection(['regression'], { 
            general: ['身高', '体重'], 
            xVariables: ['身高'], 
            yVariables: ['体重'] 
        });
        console.assert(result.valid, '正常情况验证失败');
        console.log('✓ 正常情况验证测试通过');
        
        return true;
        
    } catch (error) {
        console.error('✗ 变量选择验证测试失败:', error.message);
        return false;
    }
}

// 测试用户交互逻辑
function testUserInteractionLogic() {
    console.log('\n开始测试用户交互逻辑...');
    
    try {
        // 模拟DOM操作
        const mockDOM = {
            checkboxes: [
                { value: '身高', checked: true },
                { value: '体重', checked: true },
                { value: '年龄', checked: false },
                { value: '收入', checked: true }
            ],
            
            getSelectedVariables() {
                return this.checkboxes
                    .filter(cb => cb.checked)
                    .map(cb => cb.value);
            },
            
            selectAll(checked) {
                this.checkboxes.forEach(cb => cb.checked = checked);
            },
            
            getSelectAllState() {
                const checkedCount = this.checkboxes.filter(cb => cb.checked).length;
                const totalCount = this.checkboxes.length;
                
                if (checkedCount === 0) return 'none';
                if (checkedCount === totalCount) return 'all';
                return 'partial';
            }
        };
        
        // 测试获取选中变量
        let selected = mockDOM.getSelectedVariables();
        console.assert(selected.length === 3, '获取选中变量失败');
        console.log('✓ 获取选中变量测试通过');
        console.log(`  选中变量: ${selected.join(', ')}`);
        
        // 测试全选功能
        mockDOM.selectAll(true);
        selected = mockDOM.getSelectedVariables();
        console.assert(selected.length === 4, '全选功能失败');
        console.log('✓ 全选功能测试通过');
        
        // 测试取消全选
        mockDOM.selectAll(false);
        selected = mockDOM.getSelectedVariables();
        console.assert(selected.length === 0, '取消全选功能失败');
        console.log('✓ 取消全选功能测试通过');
        
        // 测试选择状态
        mockDOM.checkboxes[0].checked = true;
        mockDOM.checkboxes[1].checked = true;
        let state = mockDOM.getSelectAllState();
        console.assert(state === 'partial', '部分选择状态检测失败');
        console.log('✓ 部分选择状态检测测试通过');
        
        return true;
        
    } catch (error) {
        console.error('✗ 用户交互逻辑测试失败:', error.message);
        return false;
    }
}

// 测试快速分析功能
function testQuickAnalysisLogic() {
    console.log('\n开始测试快速分析功能...');
    
    try {
        // 模拟快速分析逻辑
        function performQuickAnalysis(columns) {
            const result = {
                selectedVariables: {
                    general: [...columns],
                    xVariables: columns.length >= 2 ? [columns[0]] : [],
                    yVariables: columns.length >= 2 ? [columns[1]] : []
                },
                selectedAnalyses: ['basic-stats', 'frequency-dist', 'correlation', 't-test', 'regression']
            };
            
            return result;
        }
        
        const mockColumns = ['身高', '体重', '年龄', '收入'];
        const result = performQuickAnalysis(mockColumns);
        
        // 验证结果
        console.assert(result.selectedVariables.general.length === 4, '快速分析变量选择失败');
        console.assert(result.selectedVariables.xVariables.length === 1, '快速分析自变量设置失败');
        console.assert(result.selectedVariables.yVariables.length === 1, '快速分析因变量设置失败');
        console.assert(result.selectedAnalyses.length === 5, '快速分析选项设置失败');
        
        console.log('✓ 快速分析功能测试通过');
        console.log(`  选中变量: ${result.selectedVariables.general.join(', ')}`);
        console.log(`  自变量: ${result.selectedVariables.xVariables.join(', ')}`);
        console.log(`  因变量: ${result.selectedVariables.yVariables.join(', ')}`);
        console.log(`  分析类型: ${result.selectedAnalyses.length}种`);
        
        return true;
        
    } catch (error) {
        console.error('✗ 快速分析功能测试失败:', error.message);
        return false;
    }
}

// 运行所有变量选择测试
function runVariableSelectionTests() {
    console.log('=== 变量选择功能测试 ===\n');
    
    let allPassed = true;
    
    // 运行各项测试
    allPassed &= testVariableSelection();
    allPassed &= testVariableValidation();
    allPassed &= testUserInteractionLogic();
    allPassed &= testQuickAnalysisLogic();
    
    console.log('\n=== 测试总结 ===');
    if (allPassed) {
        console.log('🎉 所有变量选择功能测试通过！');
    } else {
        console.log('❌ 部分测试失败');
    }
    
    return allPassed;
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.runVariableSelectionTests = runVariableSelectionTests;
    window.testVariableSelection = testVariableSelection;
    window.testVariableValidation = testVariableValidation;
    window.testUserInteractionLogic = testUserInteractionLogic;
    window.testQuickAnalysisLogic = testQuickAnalysisLogic;
}
