# 增强回归分析功能说明

**版本**: v2.0.0  
**更新日期**: 2025-07-19  
**功能类型**: 增强回归分析

## 功能概述

在原有简单线性回归基础上，新增了专业级的回归分析功能，提供详细的统计指标、残差分析和诊断图表，满足学术研究和专业数据分析需求。

## 新增功能特性

### 1. 详细统计指标

#### 回归系数分析
- **估计值**: 截距和斜率的点估计
- **标准误**: 回归系数的标准误差
- **t统计量**: 系数显著性检验统计量
- **p值**: 统计显著性概率值
- **置信区间**: 95%置信区间估计

#### 模型拟合优度
- **R²**: 决定系数，解释变异比例
- **调整R²**: 考虑自由度的调整决定系数
- **F统计量**: 模型整体显著性检验
- **标准误**: 回归标准误差

### 2. 专业统计表格

#### 系数汇总表 (Coefficients Summary)
```
系数    | 估计值  | 标准误  | t值    | p值   | 95%置信区间
截距    | -47.14  | 8.25   | -5.71  | 0.002 | [-66.32, -27.96]
身高    | 0.67    | 0.05   | 13.40  | <0.001| [0.56, 0.78]
```

#### 模型摘要表 (Model Summary)
```
指标        | 数值
R²         | 0.9730
调整R²      | 0.9676
标准误      | 2.8456
F统计量     | 179.56
F检验p值    | <0.001
自由度      | 5
样本量      | 7
```

#### 方差分析表 (ANOVA)
```
来源  | 自由度 | 平方和   | 均方     | F值    | p值
回归  | 1      | 1453.71  | 1453.71  | 179.56 | <0.001
残差  | 5      | 40.47    | 8.09     | -      | -
总计  | 6      | 1494.18  | -        | -      | -
```

### 3. 残差分析功能

#### 残差统计摘要
- **残差均值**: 理论上应接近0
- **残差标准差**: 残差的离散程度
- **残差范围**: 最小值和最大值
- **Durbin-Watson统计量**: 自相关检验

#### 正态性检验
- **Jarque-Bera统计量**: 正态性检验
- **偏度**: 分布的对称性
- **峰度**: 分布的尖锐程度
- **p值**: 正态性检验的显著性

#### 异常值检测
- **标准化残差**: |残差| > 2σ 的观测值
- **异常值列表**: 包含索引、残差值和拟合值
- **影响点分析**: 对回归结果影响较大的点

### 4. 诊断图表

#### 残差 vs 拟合值散点图
- **用途**: 检验线性假设和等方差假设
- **理想模式**: 残差随机分布在零线周围
- **问题识别**: 
  - 曲线模式 → 非线性关系
  - 喇叭形 → 异方差性
  - 聚集模式 → 自相关

#### 标准化残差图
- **用途**: 识别异常值和影响点
- **参考线**: ±2σ 异常值检测线
- **解释**: 超出±2σ的点可能是异常值

#### 残差正态Q-Q图
- **用途**: 检验残差正态性假设
- **理想模式**: 点应沿参考线分布
- **偏离模式**:
  - S形曲线 → 偏度问题
  - 弯曲 → 峰度问题
  - 离散 → 非正态分布

#### 回归散点图（含回归线）
- **用途**: 展示原始数据和拟合关系
- **要素**: 数据点、回归线、置信带
- **评估**: 拟合优度的直观展示

### 5. 模型诊断功能

#### 自动诊断检查
- ✅ **正态性检验**: 残差是否服从正态分布
- ✅ **异常值检测**: 是否存在极端观测值
- ✅ **自相关检验**: 残差是否存在序列相关
- ✅ **线性假设**: 关系是否为线性

#### 诊断结果解释
```
✓ 模型假设检验通过，残差分析结果良好
⚠ 检测到2个异常值，可能存在自相关问题
```

## 使用方法

### 1. 数据准备
- 上传包含至少两个数值列的Excel文件
- 确保数据质量，处理缺失值

### 2. 执行分析
1. 选择"简单回归"分析选项
2. 点击"运行选中分析"或"快速分析"
3. 系统自动对所有数值列对进行回归分析

### 3. 结果解读

#### 系数解释
- **截距**: 当自变量为0时因变量的期望值
- **斜率**: 自变量每增加1个单位，因变量的平均变化量
- **p值**: < 0.05表示系数显著不为0

#### 模型评估
- **R²**: 0.7以上表示拟合较好
- **F检验**: p < 0.05表示模型整体显著
- **残差分析**: 检查模型假设是否满足

## 技术实现

### 1. 统计计算
```javascript
// 增强回归分析主函数
performEnhancedLinearRegression(x, y, xName, yName)

// 残差分析
performResidualAnalysis(residuals, fittedValues, standardError)

// 正态性检验
testResidualNormality(residuals)
```

### 2. 图表渲染
```javascript
// 残差图表
renderResidualPlot(canvasId, fittedValues, residuals)
renderStandardizedResidualPlot(canvasId, fittedValues, standardizedResiduals)
renderQQPlot(canvasId, residuals)
```

### 3. 结果展示
```javascript
// 专业表格
createCoefficientsTable(regression)
createModelSummaryTable(regression)
createAnovaTable(regression)
createResidualAnalysisSection(regression)
```

## 应用场景

### 1. 学术研究
- 论文数据分析
- 假设检验
- 模型验证

### 2. 商业分析
- 销售预测
- 成本分析
- 绩效评估

### 3. 教学演示
- 统计学教学
- 回归分析课程
- 数据科学培训

## 输出示例

### 完整分析报告
```
体重 ~ 身高 增强回归分析

模型摘要:
- R² = 0.9730 (97.3%的变异被解释)
- 调整R² = 0.9676
- F统计量 = 179.56 (p < 0.001)

回归方程: 体重 = -47.14 + 0.67 * 身高

系数显著性:
- 截距: t = -5.71, p = 0.002 (显著)
- 斜率: t = 13.40, p < 0.001 (高度显著)

残差分析:
- 残差均值: 0.000000 (符合假设)
- 无异常值检测
- 正态性检验通过 (p = 0.15)
- 无自相关问题 (DW = 2.1)

结论: 身高对体重有显著的正向影响，每增加1cm身高，
体重平均增加0.67kg。模型拟合优度良好，假设检验通过。
```

## 质量保证

### 1. 统计准确性
- 基于成熟的统计理论
- 参考标准统计软件算法
- 多重验证和测试

### 2. 异常处理
- 样本量不足检测
- 完全共线性处理
- 数值稳定性保证

### 3. 用户友好
- 清晰的结果解释
- 专业的统计术语
- 直观的图表展示

## 后续扩展

### 计划功能
- 多元线性回归
- 非线性回归
- 逐步回归
- 岭回归和Lasso回归
- 交互效应分析

### 图表增强
- 置信带和预测带
- 影响点分析图
- 偏残差图
- Cook距离图

---

**注意**: 此功能适用于具有统计学基础的用户。建议在使用前了解线性回归的基本假设和解释方法。
