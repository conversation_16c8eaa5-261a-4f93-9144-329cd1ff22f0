<!DOCTYPE html>
<html>
<head>
    <title>功能验证</title>
    <script src="https://cdn.jsdelivr.net/npm/simple-statistics@7.8.3/dist/simple-statistics.min.js"></script>
</head>
<body>
    <h1>t检验功能验证</h1>
    <button onclick="verify()">验证功能</button>
    <div id="output"></div>

    <script src="src/utils/statisticsCalculator.js"></script>
    <script src="src/utils/tTestExplainer.js"></script>
    
    <script>
        function verify() {
            try {
                console.log('开始验证...');
                
                // 检查类是否正确加载
                console.log('StatisticsCalculator:', typeof window.statisticsCalculator);
                console.log('TTestExplainer:', typeof window.TTestExplainer);
                
                if (typeof window.statisticsCalculator === 'undefined') {
                    throw new Error('StatisticsCalculator未正确加载');
                }
                
                if (typeof window.TTestExplainer === 'undefined') {
                    throw new Error('TTestExplainer未正确加载');
                }
                
                // 创建测试数据
                const data = [25, 28, 32, 29, 35, 27, 31, 26, 33, 30];
                console.log('测试数据:', data);
                
                // 执行t检验
                const result = window.statisticsCalculator.performOneSampleTTest(data, 30);
                console.log('t检验结果:', result);
                
                // 检查结果是否包含必要字段
                const requiredFields = ['tStatistic', 'pValue', 'confidenceInterval', 'sampleMean', 'populationMean'];
                for (const field of requiredFields) {
                    if (!(field in result)) {
                        throw new Error(`缺少必要字段: ${field}`);
                    }
                }
                
                // 创建解释器
                const explainer = new window.TTestExplainer();
                console.log('解释器创建成功');
                
                // 测试术语获取
                const sampleMeanTerm = explainer.getTerm('sampleMean');
                console.log('样本均值术语:', sampleMeanTerm);
                
                // 生成业务解释
                const businessExplanation = explainer.generateBusinessExplanation(result, '测试数据');
                console.log('业务解释:', businessExplanation);
                
                // 显示结果
                document.getElementById('output').innerHTML = `
                    <h3>验证成功！</h3>
                    <p><strong>t统计量:</strong> ${result.tStatistic.toFixed(3)}</p>
                    <p><strong>p值:</strong> ${result.pValue.toFixed(3)}</p>
                    <p><strong>置信区间:</strong> [${result.confidenceInterval[0].toFixed(3)}, ${result.confidenceInterval[1].toFixed(3)}]</p>
                    <p><strong>样本均值术语:</strong> ${sampleMeanTerm}</p>
                    <h4>业务解释概要:</h4>
                    <p>${businessExplanation.summary}</p>
                `;
                
            } catch (error) {
                console.error('验证失败:', error);
                document.getElementById('output').innerHTML = `<p style="color: red;">验证失败: ${error.message}</p>`;
            }
        }
    </script>
</body>
</html>
