/**
 * Q-Q图功能测试
 * 版本: v2.1.1
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v2.1.1: 初始版本，专门测试Q-Q图渲染功能
 */

// 测试正态分位数计算
function testNormalQuantile() {
    console.log('开始测试正态分位数计算...');
    
    try {
        // 模拟chartRenderer的normalQuantile方法
        function normalQuantile(p) {
            // 边界检查
            if (p <= 0 || p >= 1) {
                throw new Error('概率值必须在0和1之间');
            }
            
            if (p === 0.5) return 0;
            
            // 使用Box-Muller变换的逆函数近似
            if (p < 0.5) {
                return -normalQuantile(1 - p);
            }
            
            // 使用Acklam算法的简化版本
            const a0 = -3.969683028665376e+01;
            const a1 = 2.209460984245205e+02;
            const a2 = -2.759285104469687e+02;
            const a3 = 1.383577518672690e+02;
            const a4 = -3.066479806614716e+01;
            const a5 = 2.506628277459239e+00;
            
            const b1 = -5.447609879822406e+01;
            const b2 = 1.615858368580409e+02;
            const b3 = -1.556989798598866e+02;
            const b4 = 6.680131188771972e+01;
            const b5 = -1.328068155288572e+01;
            
            const c0 = -7.784894002430293e-03;
            const c1 = -3.223964580411365e-01;
            const c2 = -2.400758277161838e+00;
            const c3 = -2.549732539343734e+00;
            const c4 = 4.374664141464968e+00;
            const c5 = 2.938163982698783e+00;
            
            const d1 = 7.784695709041462e-03;
            const d2 = 3.224671290700398e-01;
            const d3 = 2.445134137142996e+00;
            const d4 = 3.754408661907416e+00;
            
            const p_low = 0.02425;
            const p_high = 1 - p_low;
            
            let x;
            
            if (p < p_low) {
                const q = Math.sqrt(-2 * Math.log(p));
                x = (((((c5 * q + c4) * q + c3) * q + c2) * q + c1) * q + c0) /
                    ((((d4 * q + d3) * q + d2) * q + d1) * q + 1);
            } else if (p <= p_high) {
                const q = p - 0.5;
                const r = q * q;
                x = (((((a5 * r + a4) * r + a3) * r + a2) * r + a1) * r + a0) * q /
                    (((((b5 * r + b4) * r + b3) * r + b2) * r + b1) * r + 1);
            } else {
                const q = Math.sqrt(-2 * Math.log(1 - p));
                x = -(((((c5 * q + c4) * q + c3) * q + c2) * q + c1) * q + c0) /
                     ((((d4 * q + d3) * q + d2) * q + d1) * q + 1);
            }
            
            return x;
        }
        
        // 测试已知的分位数值
        const testCases = [
            { p: 0.5, expected: 0, tolerance: 0.001 },
            { p: 0.8413, expected: 1, tolerance: 0.01 },  // P(Z <= 1) ≈ 0.8413
            { p: 0.9772, expected: 2, tolerance: 0.01 },  // P(Z <= 2) ≈ 0.9772
            { p: 0.1587, expected: -1, tolerance: 0.01 }, // P(Z <= -1) ≈ 0.1587
            { p: 0.25, expected: -0.6745, tolerance: 0.01 }, // 第一四分位数
            { p: 0.75, expected: 0.6745, tolerance: 0.01 }   // 第三四分位数
        ];
        
        let passed = 0;
        testCases.forEach((testCase, index) => {
            try {
                const result = normalQuantile(testCase.p);
                const error = Math.abs(result - testCase.expected);
                
                if (error <= testCase.tolerance) {
                    console.log(`✓ 测试用例${index + 1}通过: p=${testCase.p}, 期望=${testCase.expected}, 实际=${result.toFixed(4)}`);
                    passed++;
                } else {
                    console.log(`✗ 测试用例${index + 1}失败: p=${testCase.p}, 期望=${testCase.expected}, 实际=${result.toFixed(4)}, 误差=${error.toFixed(4)}`);
                }
            } catch (error) {
                console.log(`✗ 测试用例${index + 1}异常: ${error.message}`);
            }
        });
        
        console.log(`✓ 正态分位数计算测试完成: ${passed}/${testCases.length}个测试通过`);
        return passed === testCases.length;
        
    } catch (error) {
        console.error('✗ 正态分位数计算测试失败:', error.message);
        return false;
    }
}

// 测试Q-Q图数据准备
function testQQDataPreparation() {
    console.log('\n开始测试Q-Q图数据准备...');
    
    try {
        // 模拟残差数据（接近正态分布）
        const residuals = [-2.1, -1.5, -0.8, -0.3, 0.1, 0.4, 0.9, 1.2, 1.8];
        
        // 模拟Q-Q数据准备过程
        function prepareQQData(residuals) {
            // 过滤有效数据
            const validResiduals = residuals.filter(r => 
                r !== null && r !== undefined && !isNaN(r) && isFinite(r)
            );
            
            if (validResiduals.length < 3) {
                throw new Error('有效残差数据不足');
            }
            
            // 排序
            const sortedResiduals = [...validResiduals].sort((a, b) => a - b);
            const n = sortedResiduals.length;
            
            const qqData = [];
            
            for (let i = 0; i < n; i++) {
                // 使用Blom plotting position
                const p = (i + 0.375) / (n + 0.25);
                
                // 简化的正态分位数计算（用于测试）
                let theoreticalQuantile;
                if (p === 0.5) {
                    theoreticalQuantile = 0;
                } else if (p < 0.5) {
                    theoreticalQuantile = -Math.sqrt(-2 * Math.log(p));
                } else {
                    theoreticalQuantile = Math.sqrt(-2 * Math.log(1 - p));
                }
                
                const sampleQuantile = sortedResiduals[i];
                
                if (isFinite(theoreticalQuantile) && isFinite(sampleQuantile)) {
                    qqData.push({ x: theoreticalQuantile, y: sampleQuantile });
                }
            }
            
            return qqData;
        }
        
        const qqData = prepareQQData(residuals);
        
        // 验证结果
        console.assert(qqData.length === residuals.length, 'Q-Q数据点数量不匹配');
        console.assert(qqData.every(point => isFinite(point.x) && isFinite(point.y)), 'Q-Q数据包含无效值');
        console.assert(qqData[0].y <= qqData[qqData.length - 1].y, 'Q-Q数据Y值未正确排序');
        
        console.log('✓ Q-Q图数据准备测试通过');
        console.log(`  生成${qqData.length}个数据点`);
        console.log(`  理论分位数范围: [${qqData[0].x.toFixed(3)}, ${qqData[qqData.length-1].x.toFixed(3)}]`);
        console.log(`  样本分位数范围: [${qqData[0].y.toFixed(3)}, ${qqData[qqData.length-1].y.toFixed(3)}]`);
        
        return true;
        
    } catch (error) {
        console.error('✗ Q-Q图数据准备测试失败:', error.message);
        return false;
    }
}

// 测试Canvas元素创建
function testCanvasCreation() {
    console.log('\n开始测试Canvas元素创建...');
    
    try {
        // 模拟createCanvas方法
        function createCanvas(canvasId, width = 400, height = 300) {
            const canvas = document.createElement('canvas');
            canvas.id = canvasId;
            canvas.width = width;
            canvas.height = height;
            canvas.className = 'max-w-full h-auto';
            return canvas;
        }
        
        // 测试Canvas创建
        const testCanvasId = 'test-qq-canvas';
        const canvas = createCanvas(testCanvasId);
        
        // 验证Canvas属性
        console.assert(canvas.id === testCanvasId, 'Canvas ID设置失败');
        console.assert(canvas.width === 400, 'Canvas宽度设置失败');
        console.assert(canvas.height === 300, 'Canvas高度设置失败');
        console.assert(canvas.className === 'max-w-full h-auto', 'Canvas样式类设置失败');
        
        // 模拟添加到DOM
        const container = document.createElement('div');
        container.appendChild(canvas);
        
        // 验证DOM查找
        const foundCanvas = container.querySelector(`#${testCanvasId}`);
        console.assert(foundCanvas === canvas, 'Canvas DOM查找失败');
        
        console.log('✓ Canvas元素创建测试通过');
        console.log(`  Canvas ID: ${canvas.id}`);
        console.log(`  Canvas尺寸: ${canvas.width}x${canvas.height}`);
        
        return true;
        
    } catch (error) {
        console.error('✗ Canvas元素创建测试失败:', error.message);
        return false;
    }
}

// 测试异步渲染时序
function testAsyncRenderingTiming() {
    console.log('\n开始测试异步渲染时序...');
    
    try {
        // 模拟DOM元素创建和渲染时序
        function simulateAsyncRendering() {
            return new Promise((resolve, reject) => {
                // 模拟DOM元素创建
                const container = document.createElement('div');
                const canvas = document.createElement('canvas');
                canvas.id = 'test-async-canvas';
                
                // 模拟异步添加到DOM
                setTimeout(() => {
                    container.appendChild(canvas);
                    
                    // 模拟图表渲染
                    setTimeout(() => {
                        const foundCanvas = container.querySelector('#test-async-canvas');
                        if (foundCanvas) {
                            resolve('渲染成功');
                        } else {
                            reject(new Error('Canvas元素未找到'));
                        }
                    }, 100); // 模拟渲染延迟
                    
                }, 50); // 模拟DOM创建延迟
            });
        }
        
        // 测试异步渲染
        return simulateAsyncRendering()
            .then(result => {
                console.log('✓ 异步渲染时序测试通过');
                console.log(`  结果: ${result}`);
                return true;
            })
            .catch(error => {
                console.error('✗ 异步渲染时序测试失败:', error.message);
                return false;
            });
        
    } catch (error) {
        console.error('✗ 异步渲染时序测试失败:', error.message);
        return Promise.resolve(false);
    }
}

// 运行所有Q-Q图测试
async function runQQPlotTests() {
    console.log('=== Q-Q图功能测试 ===\n');
    
    let allPassed = true;
    
    // 运行同步测试
    allPassed &= testNormalQuantile();
    allPassed &= testQQDataPreparation();
    allPassed &= testCanvasCreation();
    
    // 运行异步测试
    const asyncResult = await testAsyncRenderingTiming();
    allPassed &= asyncResult;
    
    console.log('\n=== 测试总结 ===');
    if (allPassed) {
        console.log('🎉 所有Q-Q图功能测试通过！');
    } else {
        console.log('❌ 部分测试失败');
    }
    
    return allPassed;
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.runQQPlotTests = runQQPlotTests;
    window.testNormalQuantile = testNormalQuantile;
    window.testQQDataPreparation = testQQDataPreparation;
    window.testCanvasCreation = testCanvasCreation;
    window.testAsyncRenderingTiming = testAsyncRenderingTiming;
}
