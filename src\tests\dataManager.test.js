/**
 * 数据管理器测试
 * 版本: v1.0.0
 * 创建日期: 2025-07-19
 * 变更记录:
 * - v1.0.0: 初始版本，测试数据管理器的核心功能
 */

// 模拟测试数据
const mockExcelData = [
    ['姓名', '年龄', '身高', '体重'],
    ['张三', 25, 175, 70],
    ['李四', 30, 180, 75],
    ['王五', 28, 170, 65],
    ['赵六', 35, 185, 80],
    ['钱七', 22, 165, 60]
];

describe('DataManager', () => {
    let dataManager;

    beforeEach(() => {
        // 模拟DataManager类
        dataManager = {
            rawData: null,
            processedData: null,
            columns: [],
            fileName: '',
            fileSize: 0,
            rowCount: 0,

            processData() {
                if (!this.rawData || this.rawData.length === 0) {
                    throw new Error('没有可处理的数据');
                }

                this.columns = this.rawData[0] || [];
                const dataRows = this.rawData.slice(1);
                this.rowCount = dataRows.length;
                
                this.processedData = dataRows.map((row, index) => {
                    const rowData = { _rowIndex: index };
                    this.columns.forEach((column, colIndex) => {
                        rowData[column] = row[colIndex];
                    });
                    return rowData;
                });
            },

            getNumericColumn(columnName) {
                if (!this.processedData) {
                    throw new Error('没有可用的数据');
                }

                return this.processedData
                    .map(row => row[columnName])
                    .filter(value => value !== null && value !== undefined && !isNaN(Number(value)))
                    .map(value => Number(value));
            },

            getNumericColumns() {
                if (!this.processedData || this.processedData.length === 0) {
                    return [];
                }

                return this.columns.filter(column => {
                    const values = this.processedData
                        .map(row => row[column])
                        .filter(value => value !== null && value !== undefined);
                    
                    const numericValues = values.filter(value => !isNaN(Number(value)));
                    return numericValues.length >= values.length * 0.5;
                });
            },

            getColumnInfo(columnName) {
                if (!this.processedData) {
                    throw new Error('没有可用的数据');
                }

                const values = this.processedData
                    .map(row => row[columnName])
                    .filter(value => value !== null && value !== undefined);

                const numericValues = values.filter(value => !isNaN(Number(value)));
                
                return {
                    name: columnName,
                    totalCount: this.processedData.length,
                    validCount: values.length,
                    nullCount: this.processedData.length - values.length,
                    isNumeric: numericValues.length >= values.length * 0.5,
                    numericCount: numericValues.length,
                    dataType: this.inferDataType(values)
                };
            },

            inferDataType(values) {
                if (values.length === 0) return 'empty';
                
                const numericValues = values.filter(value => !isNaN(Number(value)));
                const numericRatio = numericValues.length / values.length;
                
                if (numericRatio >= 0.8) return 'numeric';
                if (numericRatio >= 0.5) return 'mixed';
                
                return 'text';
            }
        };

        // 设置测试数据
        dataManager.rawData = mockExcelData;
        dataManager.processData();
    });

    test('应该正确处理Excel数据', () => {
        expect(dataManager.columns).toEqual(['姓名', '年龄', '身高', '体重']);
        expect(dataManager.rowCount).toBe(5);
        expect(dataManager.processedData).toHaveLength(5);
    });

    test('应该正确识别数值列', () => {
        const numericColumns = dataManager.getNumericColumns();
        expect(numericColumns).toEqual(['年龄', '身高', '体重']);
    });

    test('应该正确获取数值列数据', () => {
        const ageData = dataManager.getNumericColumn('年龄');
        expect(ageData).toEqual([25, 30, 28, 35, 22]);
    });

    test('应该正确获取列信息', () => {
        const ageInfo = dataManager.getColumnInfo('年龄');
        expect(ageInfo.isNumeric).toBe(true);
        expect(ageInfo.dataType).toBe('numeric');
        expect(ageInfo.validCount).toBe(5);
        expect(ageInfo.nullCount).toBe(0);

        const nameInfo = dataManager.getColumnInfo('姓名');
        expect(nameInfo.isNumeric).toBe(false);
        expect(nameInfo.dataType).toBe('text');
    });

    test('应该正确推断数据类型', () => {
        expect(dataManager.inferDataType([1, 2, 3, 4, 5])).toBe('numeric');
        expect(dataManager.inferDataType(['a', 'b', 'c'])).toBe('text');
        expect(dataManager.inferDataType([1, 2, 'a', 'b'])).toBe('mixed');
        expect(dataManager.inferDataType([])).toBe('empty');
    });

    test('处理空数据时应该抛出错误', () => {
        dataManager.rawData = null;
        expect(() => dataManager.processData()).toThrow('没有可处理的数据');
    });

    test('获取不存在列的数据时应该返回空数组', () => {
        const nonExistentData = dataManager.getNumericColumn('不存在的列');
        expect(nonExistentData).toEqual([]);
    });
});

// 运行测试的简单函数
function runTests() {
    console.log('开始运行数据管理器测试...');
    
    try {
        // 这里可以手动运行一些基本测试
        const testDataManager = {
            rawData: mockExcelData,
            processedData: null,
            columns: [],
            rowCount: 0,
            
            processData() {
                this.columns = this.rawData[0] || [];
                const dataRows = this.rawData.slice(1);
                this.rowCount = dataRows.length;
                
                this.processedData = dataRows.map((row, index) => {
                    const rowData = { _rowIndex: index };
                    this.columns.forEach((column, colIndex) => {
                        rowData[column] = row[colIndex];
                    });
                    return rowData;
                });
            },
            
            getNumericColumns() {
                return this.columns.filter(column => {
                    const values = this.processedData
                        .map(row => row[column])
                        .filter(value => value !== null && value !== undefined);
                    
                    const numericValues = values.filter(value => !isNaN(Number(value)));
                    return numericValues.length >= values.length * 0.5;
                });
            }
        };
        
        testDataManager.processData();
        
        console.log('✓ 数据处理测试通过');
        console.log('✓ 列数:', testDataManager.columns.length);
        console.log('✓ 行数:', testDataManager.rowCount);
        console.log('✓ 数值列:', testDataManager.getNumericColumns());
        
        console.log('所有测试通过！');
        return true;
    } catch (error) {
        console.error('测试失败:', error.message);
        return false;
    }
}

// 如果在浏览器环境中，将测试函数添加到全局对象
if (typeof window !== 'undefined') {
    window.runDataManagerTests = runTests;
}
