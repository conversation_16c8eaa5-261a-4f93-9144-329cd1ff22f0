<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>t检验功能测试</title>
    <script src="https://cdn.jsdelivr.net/npm/simple-statistics@7.8.3/dist/simple-statistics.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-900 mb-6">t检验功能测试</h1>
        
        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <h2 class="text-xl font-semibold mb-4">测试说明</h2>
            <p class="text-gray-700 mb-4">
                本页面用于测试新增的t检验功能，包括：
            </p>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
                <li>国际化术语对照（中英文对照）</li>
                <li>用户友好的业务解释</li>
                <li>p值计算和置信区间</li>
                <li>实际业务意义解读</li>
            </ul>
        </div>

        <div class="bg-white rounded-lg shadow p-6 mb-6">
            <button onclick="runTest()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                运行t检验测试
            </button>
        </div>

        <div id="results" class="space-y-4"></div>
    </div>

    <script src="src/utils/statisticsCalculator.js"></script>
    <script src="src/utils/tTestExplainer.js"></script>
    
    <script>
        function runTest() {
            try {
                console.log('开始测试t检验功能...');
                
                // 创建测试数据 - 模拟员工年龄数据
                const ageData = [25, 28, 32, 29, 35, 27, 31, 26, 33, 30, 34, 28, 36, 29, 32, 27, 31, 25, 33, 30];
                const hypothesizedAge = 30; // 假设平均年龄为30岁
                
                // 执行t检验
                const tTestResult = window.statisticsCalculator.performOneSampleTTest(ageData, hypothesizedAge);
                console.log('t检验结果:', tTestResult);
                
                // 创建解释器
                const explainer = new window.TTestExplainer();
                
                // 生成业务解释
                const businessExplanation = explainer.generateBusinessExplanation(tTestResult, '员工年龄');
                console.log('业务解释:', businessExplanation);
                
                // 显示结果
                const resultsDiv = document.getElementById('results');
                resultsDiv.innerHTML = `
                    <div class="bg-white border rounded-lg p-6">
                        <h3 class="text-xl font-semibold text-gray-900 mb-4">员工年龄 - t检验结果</h3>
                        
                        <!-- 统计结果 -->
                        <div class="grid grid-cols-2 gap-4 text-sm mb-4">
                            <div>
                                <p><strong>${explainer.getTerm('sampleMean')}:</strong> ${tTestResult.sampleMean.toFixed(4)}</p>
                                <p><strong>${explainer.getTerm('hypothesizedMean')}:</strong> ${tTestResult.populationMean}</p>
                                <p><strong>${explainer.getTerm('sampleStd')}:</strong> ${tTestResult.sampleStd.toFixed(4)}</p>
                                <p><strong>${explainer.getTerm('standardError')}:</strong> ${tTestResult.standardError.toFixed(4)}</p>
                            </div>
                            <div>
                                <p><strong>${explainer.getTerm('tStatistic')}:</strong> ${tTestResult.tStatistic.toFixed(4)}</p>
                                <p><strong>${explainer.getTerm('pValue')}:</strong> ${tTestResult.pValue < 0.001 ? '<0.001' : tTestResult.pValue.toFixed(3)}</p>
                                <p><strong>${explainer.getTerm('degreesOfFreedom')}:</strong> ${tTestResult.degreesOfFreedom}</p>
                                <p><strong>${explainer.getTerm('sampleSize')}:</strong> ${tTestResult.n}</p>
                                <p><strong>${explainer.getTerm('significanceLevel')}:</strong> ${tTestResult.alpha}</p>
                            </div>
                        </div>
                        
                        <!-- 置信区间 -->
                        <div class="mb-4 p-3 bg-gray-50 rounded">
                            <p class="text-sm"><strong>${explainer.getTerm('confidenceInterval')} (${((1-tTestResult.alpha)*100).toFixed(0)}%):</strong> 
                            [${tTestResult.confidenceInterval[0].toFixed(3)}, ${tTestResult.confidenceInterval[1].toFixed(3)}]</p>
                        </div>
                        
                        <!-- 统计结论 -->
                        <div class="mb-4 p-3 bg-blue-50 rounded">
                            <p class="text-blue-800 text-sm"><strong>统计结论 (Statistical Conclusion):</strong> ${tTestResult.interpretation}</p>
                        </div>
                        
                        <!-- 业务解释 -->
                        ${explainer.generateBusinessHTML(businessExplanation)}
                    </div>
                `;
                
                console.log('测试完成！');
                
            } catch (error) {
                console.error('测试失败:', error);
                document.getElementById('results').innerHTML = `
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <p class="text-red-800"><strong>错误:</strong> ${error.message}</p>
                        <p class="text-red-600 text-sm mt-2">请检查浏览器控制台获取更多详细信息。</p>
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
