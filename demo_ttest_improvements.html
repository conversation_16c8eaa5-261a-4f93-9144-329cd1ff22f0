<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>t检验功能改进演示</title>
    <script src="https://cdn.jsdelivr.net/npm/simple-statistics@7.8.3/dist/simple-statistics.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto">
            <h1 class="text-4xl font-bold text-center text-gray-900 mb-8">
                t检验功能改进演示
            </h1>
            
            <!-- 功能介绍 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">🚀 新功能特性</h2>
                <div class="grid md:grid-cols-2 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-blue-600 mb-2">1. 国际化术语对照</h3>
                        <ul class="text-gray-700 space-y-1">
                            <li>• 样本均值 (Sample Mean)</li>
                            <li>• t统计量 (t-Statistic)</li>
                            <li>• p值 (p-value)</li>
                            <li>• 置信区间 (Confidence Interval)</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="text-lg font-medium text-green-600 mb-2">2. 业务解释功能</h3>
                        <ul class="text-gray-700 space-y-1">
                            <li>• 通俗易懂的结果说明</li>
                            <li>• 实际业务意义解读</li>
                            <li>• 可信度评估</li>
                            <li>• 行动建议</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 演示区域 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                <h2 class="text-2xl font-semibold text-gray-800 mb-4">📊 功能演示</h2>
                <div class="flex flex-wrap gap-4 mb-6">
                    <button onclick="demoEmployeeAge()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        演示：员工年龄分析
                    </button>
                    <button onclick="demoSalaryAnalysis()" class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors">
                        演示：薪资水平分析
                    </button>
                    <button onclick="demoEducationYears()" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors">
                        演示：教育年限分析
                    </button>
                </div>
                <div id="demo-results" class="space-y-6"></div>
            </div>

            <!-- 对比展示 -->
            <div class="grid md:grid-cols-2 gap-6">
                <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-red-800 mb-3">❌ 改进前</h3>
                    <ul class="text-red-700 space-y-2 text-sm">
                        <li>• 只有中文术语，缺乏国际化支持</li>
                        <li>• 统计结果过于技术化</li>
                        <li>• 缺少p值和置信区间</li>
                        <li>• 没有业务层面的解释</li>
                        <li>• 用户难以理解实际意义</li>
                    </ul>
                </div>
                <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-green-800 mb-3">✅ 改进后</h3>
                    <ul class="text-green-700 space-y-2 text-sm">
                        <li>• 中英文术语对照，支持国际化</li>
                        <li>• 完整的统计指标展示</li>
                        <li>• 准确的p值和置信区间计算</li>
                        <li>• 通俗易懂的业务解释</li>
                        <li>• 实用的行动建议</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="src/utils/statisticsCalculator.js"></script>
    <script src="src/utils/tTestExplainer.js"></script>
    
    <script>
        function createTTestDemo(data, hypothesizedValue, fieldName, description) {
            try {
                // 执行t检验
                const tTestResult = window.statisticsCalculator.performOneSampleTTest(data, hypothesizedValue);
                
                // 创建解释器
                const explainer = new window.TTestExplainer();
                
                // 生成业务解释
                const businessExplanation = explainer.generateBusinessExplanation(tTestResult, fieldName);
                
                return `
                    <div class="border rounded-lg p-6 bg-white">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2">${fieldName}分析</h3>
                        <p class="text-gray-600 mb-4">${description}</p>
                        
                        <!-- 统计结果 -->
                        <div class="grid grid-cols-2 gap-4 text-sm mb-4 bg-gray-50 p-4 rounded">
                            <div>
                                <p><strong>${explainer.getTerm('sampleMean')}:</strong> ${tTestResult.sampleMean.toFixed(2)}</p>
                                <p><strong>${explainer.getTerm('hypothesizedMean')}:</strong> ${tTestResult.populationMean}</p>
                                <p><strong>${explainer.getTerm('sampleStd')}:</strong> ${tTestResult.sampleStd.toFixed(2)}</p>
                            </div>
                            <div>
                                <p><strong>${explainer.getTerm('tStatistic')}:</strong> ${tTestResult.tStatistic.toFixed(3)}</p>
                                <p><strong>${explainer.getTerm('pValue')}:</strong> ${tTestResult.pValue < 0.001 ? '<0.001' : tTestResult.pValue.toFixed(3)}</p>
                                <p><strong>${explainer.getTerm('sampleSize')}:</strong> ${tTestResult.n}</p>
                            </div>
                        </div>
                        
                        <!-- 置信区间 -->
                        <div class="mb-4 p-3 bg-blue-50 rounded">
                            <p class="text-sm"><strong>${explainer.getTerm('confidenceInterval')} (95%):</strong> 
                            [${tTestResult.confidenceInterval[0].toFixed(2)}, ${tTestResult.confidenceInterval[1].toFixed(2)}]</p>
                        </div>
                        
                        <!-- 业务解释 -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 class="font-semibold text-green-800 mb-2">📊 业务解释</h4>
                            <div class="space-y-2 text-sm text-green-700">
                                <p><strong>结果概要：</strong>${businessExplanation.summary}</p>
                                <p><strong>实际意义：</strong>${businessExplanation.practicalMeaning}</p>
                                <p><strong>行动建议：</strong>${businessExplanation.actionRecommendation}</p>
                            </div>
                        </div>
                    </div>
                `;
            } catch (error) {
                return `<div class="bg-red-50 border border-red-200 rounded p-4">
                    <p class="text-red-800">分析失败: ${error.message}</p>
                </div>`;
            }
        }

        function demoEmployeeAge() {
            const ageData = [25, 28, 32, 29, 35, 27, 31, 26, 33, 30, 34, 28, 36, 29, 32, 27, 31, 25, 33, 30];
            const result = createTTestDemo(ageData, 30, '员工年龄', '分析公司员工年龄是否符合预期的30岁平均水平');
            document.getElementById('demo-results').innerHTML = result;
        }

        function demoSalaryAnalysis() {
            const salaryData = [45000, 52000, 58000, 48000, 65000, 46000, 55000, 43000, 62000, 51000, 59000, 49000, 68000, 50000, 57000, 47000, 54000, 44000, 61000, 52000];
            const result = createTTestDemo(salaryData, 50000, '员工薪资', '检验员工薪资水平是否达到预期的50000元标准');
            document.getElementById('demo-results').innerHTML = result;
        }

        function demoEducationYears() {
            const educationData = [16, 18, 16, 17, 20, 15, 18, 14, 19, 17, 18, 16, 21, 17, 18, 15, 17, 14, 19, 16];
            const result = createTTestDemo(educationData, 16, '教育年限', '评估员工教育背景是否满足16年的基本要求');
            document.getElementById('demo-results').innerHTML = result;
        }

        // 页面加载时显示默认演示
        window.onload = function() {
            demoEmployeeAge();
        };
    </script>
</body>
</html>
